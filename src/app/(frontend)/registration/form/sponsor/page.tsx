"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Building2 } from "lucide-react"

const sponsorshipTiers = [
  {
    id: "platinum",
    name: "Platinum Sponsor",
    price: "KES 500,000",
    benefits: [
      "Premium brand visibility",
      "Keynote speaking opportunity",
      "VIP networking access",
      "Custom exhibition space",
      "Year-round partnership",
    ],
  },
  {
    id: "gold",
    name: "Gold Sponsor",
    price: "KES 300,000",
    benefits: [
      "High brand visibility",
      "Panel discussion opportunity",
      "Networking privileges",
      "Exhibition space",
      "Marketing materials inclusion",
    ],
  },
  {
    id: "silver",
    name: "Silver Sponsor",
    price: "KES 150,000",
    benefits: [
      "Brand visibility",
      "Networking access",
      "Logo placement",
      "Conference materials",
      "Social media promotion",
    ],
  },
  {
    id: "bronze",
    name: "Bronze Sponsor",
    price: "KES 75,000",
    benefits: [
      "Logo placement",
      "Conference access",
      "Basic networking",
      "Certificate of partnership",
    ],
  },
]

const sponsorshipAreas = [
  "Conference Sessions",
  "Networking Events",
  "Welcome Reception",
  "Gala Dinner",
  "Coffee Breaks",
  "Conference Materials",
  "Technology & Innovation Showcase",
  "Cultural Performances",
  "Site Visits",
  "Awards Ceremony",
]

const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Other",
]

export default function SponsorRegistrationForm() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    // Company Information
    companyName: "",
    contactPerson: "",
    email: "",
    phone: "",
    website: "",
    country: "",
    city: "",
    address: "",
    
    // Company Details
    companyDescription: "",
    industryType: "",
    companySize: "",
    annualRevenue: "",
    
    // Sponsorship Information
    selectedTier: "",
    sponsorshipAreas: [] as string[],
    customRequirements: "",
    marketingObjectives: "",
    
    // Brand Information
    brandGuidelines: "",
    logoFiles: null as File | null,
    marketingMaterials: "",
    
    // Representatives
    representative1Name: "",
    representative1Email: "",
    representative1Position: "",
    representative2Name: "",
    representative2Email: "",
    representative2Position: "",
    
    // Agreements
    termsAccepted: false,
    sponsorshipAgreement: false,
    marketingConsent: false,
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSponsorshipAreaChange = (area: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      sponsorshipAreas: checked 
        ? [...prev.sponsorshipAreas, area]
        : prev.sponsorshipAreas.filter(a => a !== area)
    }))
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setFormData(prev => ({
        ...prev,
        logoFiles: file
      }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Sponsor form submitted:", formData)
    router.push("/registration/review")
  }

  const selectedTierDetails = sponsorshipTiers.find(tier => tier.id === formData.selectedTier)

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>
        
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-orange-700 to-red-700 rounded-2xl flex items-center justify-center">
            <Building2 className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Conference Sponsor Registration
            </h1>
            <p className="text-orange-600 font-semibold">Partner for Impact</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Sponsorship Tier Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Select Sponsorship Tier
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {sponsorshipTiers.map((tier) => (
                <div
                  key={tier.id}
                  className={`
                    border-2 rounded-lg p-6 cursor-pointer transition-all
                    ${formData.selectedTier === tier.id 
                      ? 'border-orange-500 bg-orange-50' 
                      : 'border-gray-200 hover:border-orange-300'
                    }
                  `}
                  onClick={() => handleInputChange("selectedTier", tier.id)}
                >
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-bold text-gray-900">{tier.name}</h3>
                    <span className="text-xl font-bold text-orange-600">{tier.price}</span>
                  </div>
                  <ul className="space-y-2">
                    {tier.benefits.map((benefit, index) => (
                      <li key={index} className="text-sm text-gray-700 flex items-center">
                        <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
            
            {selectedTierDetails && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-semibold text-green-900 mb-2">Selected: {selectedTierDetails.name}</h4>
                <p className="text-green-800">Investment: {selectedTierDetails.price}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Company Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Company Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="companyName">Company/Organization Name *</Label>
              <Input
                id="companyName"
                value={formData.companyName}
                onChange={(e) => handleInputChange("companyName", e.target.value)}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="contactPerson">Primary Contact Person *</Label>
                <Input
                  id="contactPerson"
                  value={formData.contactPerson}
                  onChange={(e) => handleInputChange("contactPerson", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange("website", e.target.value)}
                  placeholder="https://www.example.com"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select value={formData.country} onValueChange={(value) => handleInputChange("country", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="companyDescription">Company Description *</Label>
              <Textarea
                id="companyDescription"
                placeholder="Provide a detailed description of your company and its mission..."
                value={formData.companyDescription}
                onChange={(e) => handleInputChange("companyDescription", e.target.value)}
                required
                rows={4}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="industryType">Industry Type *</Label>
                <Input
                  id="industryType"
                  value={formData.industryType}
                  onChange={(e) => handleInputChange("industryType", e.target.value)}
                  required
                  placeholder="e.g., Technology, Healthcare, Finance"
                />
              </div>
              <div>
                <Label htmlFor="companySize">Company Size</Label>
                <Select value={formData.companySize} onValueChange={(value) => handleInputChange("companySize", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select company size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-10">1-10 employees</SelectItem>
                    <SelectItem value="11-50">11-50 employees</SelectItem>
                    <SelectItem value="51-200">51-200 employees</SelectItem>
                    <SelectItem value="201-1000">201-1000 employees</SelectItem>
                    <SelectItem value="1000+">1000+ employees</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sponsorship Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Sponsorship Preferences
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label className="text-base font-semibold mb-4 block">
                Specific Sponsorship Areas of Interest (Select all that apply)
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {sponsorshipAreas.map((area) => (
                  <div key={area} className="flex items-center space-x-2">
                    <Checkbox
                      id={area}
                      checked={formData.sponsorshipAreas.includes(area)}
                      onCheckedChange={(checked) => handleSponsorshipAreaChange(area, checked as boolean)}
                    />
                    <Label htmlFor={area} className="text-sm">
                      {area}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="marketingObjectives">Marketing Objectives</Label>
              <Textarea
                id="marketingObjectives"
                placeholder="What are your key marketing objectives for this sponsorship?"
                value={formData.marketingObjectives}
                onChange={(e) => handleInputChange("marketingObjectives", e.target.value)}
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="customRequirements">Custom Requirements</Label>
              <Textarea
                id="customRequirements"
                placeholder="Any specific requirements or custom sponsorship ideas..."
                value={formData.customRequirements}
                onChange={(e) => handleInputChange("customRequirements", e.target.value)}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Continue Button */}
        <div className="flex justify-center">
          <Button
            type="submit"
            className="px-12 py-3 text-lg font-semibold"
            style={{ fontFamily: "Myriad Pro, Arial, sans-serif" }}
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
