'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Users,
  Target,
  TrendingUp,
  Globe,
  CheckCircle,
  Lightbulb,
  Award,
  Building,
  ArrowRight,
  Download,
  Handshake,
  Shield,
  Sparkles,
  Zap,
  Play,
} from 'lucide-react'

export default function AboutPage() {
  const [activeCard, setActiveCard] = useState<number | null>(null)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [scrollY, setScrollY] = useState(0)

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const objectives = [
    {
      id: 1,
      title: 'Innovation & Knowledge Sharing',
      description:
        'Foster innovation and knowledge sharing among indigenous communities and modern enterprises to create sustainable development pathways',
      icon: Target,
      gradient: 'from-[#159147] via-[#159147] to-[#0f7a3a]',
      hoverGradient: 'from-[#1ba855] via-[#159147] to-[#0f7a3a]',
      accentColor: 'text-[#159147]',
      bgGlow: 'shadow-[#159147]/30',
      borderGlow: 'border-[#159147]/40',
      delay: '0ms',
    },
    {
      id: 2,
      title: 'Sustainable Investment',
      description:
        'Create sustainable investment opportunities that preserve cultural heritage while driving economic growth and community empowerment',
      icon: Users,
      gradient: 'from-[#7E2518] via-[#7E2518] to-[#5a1a10]',
      hoverGradient: 'from-[#9a2e20] via-[#7E2518] to-[#5a1a10]',
      accentColor: 'text-[#7E2518]',
      bgGlow: 'shadow-[#7E2518]/30',
      borderGlow: 'border-[#7E2518]/40',
      delay: '150ms',
    },
    {
      id: 3,
      title: 'Strategic Partnerships',
      description:
        'Build strategic partnerships between traditional knowledge holders and contemporary investors for mutual benefit and growth',
      icon: Handshake,
      gradient: 'from-[#E8B32C] via-[#E8B32C] to-[#d19d1f]',
      hoverGradient: 'from-[#edc247] via-[#E8B32C] to-[#d19d1f]',
      accentColor: 'text-[#E8B32C]',
      bgGlow: 'shadow-[#E8B32C]/30',
      borderGlow: 'border-[#E8B32C]/40',
      delay: '300ms',
    },
    {
      id: 4,
      title: 'Intellectual Property Protection',
      description:
        'Develop comprehensive frameworks for protecting and commercializing indigenous intellectual property rights',
      icon: Shield,
      gradient: 'from-[#C86E36] via-[#C86E36] to-[#a85a2b]',
      hoverGradient: 'from-[#d4804a] via-[#C86E36] to-[#a85a2b]',
      accentColor: 'text-[#C86E36]',
      bgGlow: 'shadow-[#C86E36]/30',
      borderGlow: 'border-[#C86E36]/40',
      delay: '450ms',
    },
    {
      id: 5,
      title: 'Digital Transformation',
      description:
        'Leverage cutting-edge technology to bridge traditional wisdom with modern digital solutions for global impact',
      icon: Zap,
      gradient: 'from-[#81B1DB] via-[#81B1DB] to-[#6a9bc7]',
      hoverGradient: 'from-[#95c1e3] via-[#81B1DB] to-[#6a9bc7]',
      accentColor: 'text-[#81B1DB]',
      bgGlow: 'shadow-[#81B1DB]/30',
      borderGlow: 'border-[#81B1DB]/40',
      delay: '600ms',
    },
    {
      id: 6,
      title: 'Global Network Expansion',
      description:
        'Establish worldwide connections and collaborative networks to amplify indigenous voices on the international stage',
      icon: Globe,
      gradient: 'from-[#159147] via-[#7E2518] to-[#E8B32C]',
      hoverGradient: 'from-[#1ba855] via-[#9a2e20] to-[#edc247]',
      accentColor: 'text-[#159147]',
      bgGlow: 'shadow-[#159147]/30',
      borderGlow: 'border-[#159147]/40',
      delay: '750ms',
    },
  ]

  const importancePoints = [
    {
      icon: Globe,
      title: 'Global Recognition',
      description:
        'IKIAs represent a vital part of cultural heritage with significant ecological and economic value for sustainable development.',
    },
    {
      icon: TrendingUp,
      title: 'Economic Development',
      description:
        'They play a key role across sectors including health, cosmetics, agriculture, and eco-tourism, driving innovation.',
    },
    {
      icon: Target,
      title: 'Strategic Innovation',
      description:
        'Transforming traditional knowledge and natural resources helps protect biodiversity and promote sustainable practices.',
    },
    {
      icon: Users,
      title: 'Community Empowerment',
      description:
        'They empower local communities by generating ownership, innovation, and sustainable livelihoods for future generations.',
    },
    {
      icon: CheckCircle,
      title: 'Policy Support',
      description:
        'IKIAs support inclusive and sustainable development by bridging cultural identity with modern innovation frameworks.',
    },
  ]

  const targetAudience = [
    {
      number: '01',
      title: 'Investors & Entrepreneurs',
      subtitle: 'New partnerships & investments',
      bgColor: 'bg-[#159147]',
      icon: Building,
    },
    {
      number: '02',
      title: 'Indigenous Communities',
      subtitle: 'Greater IKIA visibility & protection',
      bgColor: 'bg-[#7E2518]',
      icon: Users,
    },
    {
      number: '03',
      title: 'Policymakers',
      subtitle: 'Stronger national innovation systems',
      bgColor: 'bg-[#E8B32C]',
      icon: Award,
    },
    {
      number: '04',
      title: 'Researchers & Academia',
      subtitle: 'Enhanced research & development opportunities',
      bgColor: 'bg-[#C86E36]',
      icon: Lightbulb,
    },
    {
      number: '05',
      title: 'Development Partners',
      subtitle: 'Greater IKIA visibility & protection',
      bgColor: 'bg-[#81B1DB]',
      icon: Globe,
    },
    {
      number: '06',
      title: 'Financial Institutions',
      subtitle: 'Stronger national innovation systems',
      bgColor: 'bg-[#159147]',
      icon: TrendingUp,
    },
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Smaller Geometric Box */}
      <section className="relative min-h-screen bg-gradient-to-br from-[#7E2518]/3 via-white to-[#159147]/3 overflow-hidden">
        {/* Subtle Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -left-40 w-60 h-60 bg-[#E8B32C]/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -right-40 w-72 h-72 bg-[#81B1DB]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 py-16 lg:py-24 relative z-10">
          <div className="grid lg:grid-cols-12 gap-12 items-center min-h-[80vh]">
            {/* Left Side - Smaller Geometric Design */}
            <div className="lg:col-span-5 relative">
              <div className="relative w-full h-[350px] lg:h-[400px] max-w-md mx-auto">
                {/* Main geometric container - much smaller */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#7E2518]/8 via-[#7E2518]/4 to-[#159147]/8 transform rotate-45 rounded-2xl shadow-lg border border-[#7E2518]/15">
                  {/* Inner pattern */}
                  <div className="absolute inset-6 border border-[#E8B32C]/20 rounded-xl"></div>
                </div>

                {/* Diagonal lines - adjusted for smaller size */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-full h-full relative">
                    {/* Primary diagonal lines */}
                    <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#7E2518]/25 to-transparent transform rotate-45 origin-left"></div>
                    <div className="absolute top-0 right-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#7E2518]/25 to-transparent transform -rotate-45 origin-right"></div>

                    {/* Center cross */}
                    <div className="absolute top-1/2 left-1/2 w-0.5 h-full bg-gradient-to-b from-transparent via-[#7E2518]/20 to-transparent transform -translate-x-1/2 -translate-y-1/2"></div>
                    <div className="absolute top-1/2 left-1/2 w-full h-0.5 bg-gradient-to-r from-transparent via-[#7E2518]/20 to-transparent transform -translate-x-1/2 -translate-y-1/2"></div>
                  </div>
                </div>

                {/* Smaller accent shapes */}
                <div className="absolute top-8 right-8 w-12 h-12 bg-gradient-to-br from-[#E8B32C]/15 to-[#C86E36]/15 transform rotate-12 rounded-lg border border-[#E8B32C]/25 shadow-sm hover:scale-105 transition-transform duration-300"></div>

                <div className="absolute bottom-8 left-8 w-10 h-10 bg-gradient-to-br from-[#81B1DB]/15 to-[#159147]/15 transform -rotate-12 rounded-lg border border-[#81B1DB]/25 shadow-sm hover:scale-105 transition-transform duration-300"></div>

                {/* Small floating elements */}
                <div className="absolute top-1/4 right-16 w-2 h-2 bg-[#E8B32C]/40 rounded-full"></div>
                <div className="absolute bottom-1/4 left-16 w-2 h-2 bg-[#81B1DB]/40 rounded-full"></div>
              </div>
            </div>

            {/* Right Side - Content Container (now takes more space) */}
            <div className="lg:col-span-7 relative z-20 lg:-ml-8">
              <div className="bg-white/98 backdrop-blur-xl rounded-3xl p-8 lg:p-12 shadow-xl border border-gray-100/50 relative overflow-hidden">
                {/* Minimal background pattern */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-[#7E2518]/3 to-transparent rounded-bl-3xl"></div>

                {/* Content */}
                <div className="relative z-10">
                  {/* Conference Badge */}
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#7E2518]/8 to-[#159147]/8 px-4 py-2 rounded-full border border-[#7E2518]/15 mb-6">
                    <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
                    <span
                      className="text-sm font-medium text-[#7E2518]"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      IKIA 2024 Conference
                    </span>
                  </div>

                  {/* Main Heading */}
                  <div className="space-y-6 mb-10">
                    <h1
                      className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#7E2518] leading-tight tracking-tight"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      ABOUT
                      <span className="block text-transparent bg-clip-text bg-gradient-to-r from-[#7E2518] to-[#159147]">
                        CONFERENCE
                      </span>
                    </h1>
                    <div className="w-24 h-1 bg-gradient-to-r from-[#E8B32C] to-[#C86E36] rounded-full"></div>
                    <p
                      className="text-lg md:text-xl text-gray-700 leading-relaxed max-w-lg"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      IKIA INVESTMENT CONFERENCE aligned with Kenya Vision 2030, BetA, and
                      NMK&apos;s Strategic Plan for sustainable indigenous knowledge development.
                    </p>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 mb-10">
                    <Button
                      className="group bg-gradient-to-r from-[#7E2518] to-[#7E2518]/90 hover:from-[#7E2518]/90 hover:to-[#159147] text-white px-8 py-4 rounded-xl font-bold transition-all duration-500 shadow-lg hover:shadow-xl hover:shadow-[#7E2518]/20 transform hover:-translate-y-1"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                      Explore Conference
                      <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>

                    <Button
                      variant="outline"
                      className="group border-2 border-[#159147] text-[#159147] hover:bg-[#159147] hover:text-white px-8 py-4 rounded-xl font-bold transition-all duration-500 bg-transparent hover:shadow-lg hover:shadow-[#159147]/20 transform hover:-translate-y-1"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      <Download className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                      Download Brochure
                    </Button>
                  </div>

                  {/* Statistics Cards */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <Card className="group bg-gradient-to-br from-[#159147] to-[#159147]/80 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 cursor-pointer overflow-hidden relative">
                      <div className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-bl-3xl"></div>
                      <CardContent className="p-6 text-center relative z-10">
                        <div
                          className="text-3xl lg:text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          13
                        </div>
                        <div
                          className="text-sm opacity-90 font-medium leading-tight"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          Participating Countries
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="group bg-gradient-to-br from-[#7E2518] to-[#7E2518]/80 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 cursor-pointer overflow-hidden relative">
                      <div className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-bl-3xl"></div>
                      <CardContent className="p-6 text-center relative z-10">
                        <div
                          className="text-3xl lg:text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          40
                        </div>
                        <div
                          className="text-sm opacity-90 font-medium leading-tight"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          Indigenous Knowledge Assets
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="group bg-gradient-to-br from-[#E8B32C] to-[#C86E36] text-white border-0 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 cursor-pointer overflow-hidden relative">
                      <div className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-bl-3xl"></div>
                      <CardContent className="p-6 text-center relative z-10">
                        <div
                          className="text-3xl lg:text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          25
                        </div>
                        <div
                          className="text-sm opacity-90 font-medium leading-tight"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          Indigenous Knowledge Assets
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Additional Info */}
                  <div className="mt-8 pt-6 border-t border-gray-100">
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#E8B32C] rounded-full"></div>
                        <span style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                          March 15-17, 2024
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#81B1DB] rounded-full"></div>
                        <span style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                          Nairobi, Kenya
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#159147] rounded-full"></div>
                        <span style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                          500+ Attendees
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-[#7E2518]/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-[#7E2518]/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Rest of the sections remain the same */}
      {/* Objectives Section */}
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black relative overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#159147]/10 via-[#7E2518]/10 to-[#E8B32C]/10"></div>

        {/* Brand Color Orbs */}
        <div
          className="absolute top-20 left-20 w-96 h-96 bg-[#159147] rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"
          style={{ transform: `translateY(${scrollY * 0.1}px)` }}
        ></div>
        <div
          className="absolute top-40 right-20 w-80 h-80 bg-[#7E2518] rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"
          style={{ transform: `translateY(${scrollY * -0.1}px)` }}
        ></div>
        <div
          className="absolute bottom-20 left-40 w-72 h-72 bg-[#E8B32C] rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"
          style={{ transform: `translateY(${scrollY * 0.05}px)` }}
        ></div>
        <div
          className="absolute top-1/2 right-1/3 w-64 h-64 bg-[#C86E36] rounded-full mix-blend-multiply filter blur-3xl opacity-15 animate-blob animation-delay-6000"
          style={{ transform: `translateY(${scrollY * -0.08}px)` }}
        ></div>
        <div
          className="absolute bottom-40 right-20 w-56 h-56 bg-[#81B1DB] rounded-full mix-blend-multiply filter blur-3xl opacity-15 animate-blob animation-delay-8000"
          style={{ transform: `translateY(${scrollY * 0.12}px)` }}
        ></div>

        {/* Particle Effect Background */}
        <div className="absolute inset-0">
          {[...Array(80)].map((_, i) => {
            const colors = ['#159147', '#7E2518', '#E8B32C', '#C86E36', '#81B1DB']
            const color = colors[i % colors.length]
            return (
              <div
                key={i}
                className="absolute w-1 h-1 rounded-full opacity-30 animate-pulse"
                style={{
                  backgroundColor: color,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 2}s`,
                  animationDuration: `${2 + Math.random() * 3}s`,
                }}
              ></div>
            )
          })}
        </div>

        {/* Geometric Patterns */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 border-2 border-[#159147] rotate-45 animate-spin-slow"></div>
          <div className="absolute top-3/4 right-1/4 w-24 h-24 border-2 border-[#E8B32C] rotate-12 animate-pulse"></div>
          <div className="absolute top-1/2 left-3/4 w-20 h-20 border-2 border-[#81B1DB] -rotate-45 animate-bounce-slow"></div>
        </div>

        {/* Main Content */}
        <div className="relative z-10 container mx-auto px-6 py-16 lg:py-20">
          {/* Header Section */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-3 mb-8 px-8 py-4 bg-gradient-to-r from-[#159147]/20 via-[#E8B32C]/20 to-[#81B1DB]/20 backdrop-blur-md rounded-full border border-white/30 shadow-2xl">
              <Sparkles className="w-6 h-6 text-[#E8B32C] animate-pulse" />
              <span className="text-white/90 font-medium tracking-wider text-sm uppercase">
                Objectives of the Conference
              </span>
              <Lightbulb className="w-6 h-6 text-[#159147] animate-pulse" />
            </div>

            <h1 className="text-7xl md:text-9xl font-black text-transparent bg-clip-text bg-gradient-to-r from-[#159147] via-[#E8B32C] to-[#81B1DB] mb-8 animate-fade-in-up drop-shadow-2xl">
              TRANSFORM
            </h1>
            <h2 className="text-4xl md:text-5xl font-light text-transparent bg-clip-text bg-gradient-to-r from-white via-[#81B1DB]/80 to-white animate-fade-in-up animation-delay-300">
              The Future Together
            </h2>

            <div className="w-40 h-2 bg-gradient-to-r from-[#159147] via-[#E8B32C] to-[#C86E36] mx-auto mt-10 rounded-full animate-fade-in-up animation-delay-600 shadow-lg"></div>
          </div>

          {/* Objectives Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 max-w-8xl mx-auto">
            {objectives.map((objective) => {
              const IconComponent = objective.icon
              const isActive = activeCard === objective.id

              return (
                <div
                  key={objective.id}
                  className={`group relative transform transition-all duration-700 hover:scale-110 hover:-translate-y-4 animate-fade-in-up cursor-pointer`}
                  style={{ animationDelay: objective.delay }}
                  onMouseEnter={() => setActiveCard(objective.id)}
                  onMouseLeave={() => setActiveCard(null)}
                >
                  {/* Card Background with Animated Border */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 group-hover:${objective.borderGlow} transition-all duration-500 ${objective.bgGlow} group-hover:shadow-2xl`}
                  ></div>

                  {/* Animated Border Glow */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-r ${objective.gradient} rounded-3xl opacity-0 group-hover:opacity-30 blur-2xl transition-all duration-700 animate-pulse-slow`}
                  ></div>

                  {/* Floating Ring Effect */}
                  {isActive && (
                    <div className="absolute -inset-4 rounded-3xl border-2 border-white/20 animate-ping"></div>
                  )}

                  {/* Content */}
                  <div className="relative p-10 h-full flex flex-col">
                    {/* Icon and Number */}
                    <div className="flex items-center justify-between mb-6">
                      <div
                        className={`p-5 bg-gradient-to-br ${objective.gradient} rounded-2xl shadow-2xl transform group-hover:scale-125 group-hover:rotate-6 transition-all duration-700 relative overflow-hidden`}
                      >
                        <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <IconComponent className="w-10 h-10 text-white relative z-10" />
                      </div>
                      <div
                        className={`text-7xl font-black ${objective.accentColor} opacity-15 group-hover:opacity-50 transition-all duration-700 group-hover:scale-110`}
                      >
                        0{objective.id}
                      </div>
                    </div>

                    {/* Title */}
                    <h3
                      className={`text-2xl font-bold text-white mb-6 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r ${objective.gradient.replace('from-', 'group-hover:from-').replace('via-', 'group-hover:via-').replace('to-', 'group-hover:to-')} transition-all duration-700`}
                    >
                      {objective.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-300 leading-relaxed flex-grow group-hover:text-white/95 transition-all duration-700 text-base">
                      {objective.description}
                    </p>

                    {/* Progress Bar Animation */}
                    <div className="mt-8 h-2 bg-white/10 rounded-full overflow-hidden relative">
                      <div
                        className={`h-full bg-gradient-to-r ${objective.gradient} rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1200 ease-out shadow-lg`}
                      ></div>
                      <div
                        className={`absolute inset-0 bg-gradient-to-r ${objective.gradient} rounded-full opacity-50 blur-sm transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1200 ease-out`}
                      ></div>
                    </div>

                    {/* Floating Elements */}
                    {isActive && (
                      <div className="absolute inset-0 pointer-events-none">
                        {[...Array(20)].map((_, i) => (
                          <div
                            key={i}
                            className={`absolute w-3 h-3 rounded-full animate-ping opacity-60`}
                            style={{
                              backgroundColor: objective.gradient.includes('#159147')
                                ? '#159147'
                                : objective.gradient.includes('#7E2518')
                                  ? '#7E2518'
                                  : objective.gradient.includes('#E8B32C')
                                    ? '#E8B32C'
                                    : objective.gradient.includes('#C86E36')
                                      ? '#C86E36'
                                      : '#81B1DB',
                              left: `${20 + Math.random() * 60}%`,
                              top: `${20 + Math.random() * 60}%`,
                              animationDelay: `${i * 0.05}s`,
                              animationDuration: '1.5s',
                            }}
                          ></div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-20">
            <div className="inline-flex items-center gap-4 px-10 py-5 bg-gradient-to-r from-[#159147] via-[#E8B32C] to-[#C86E36] rounded-full text-white font-bold hover:scale-110 transform transition-all duration-500 cursor-pointer shadow-2xl hover:shadow-[#159147]/40 text-lg">
              <span>Experience the Future</span>
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Mouse Follower */}
        <div
          className="absolute w-[500px] h-[500px] bg-gradient-radial from-[#159147]/15 via-[#E8B32C]/10 to-transparent rounded-full pointer-events-none transition-all duration-500 ease-out"
          style={{
            left: mousePosition.x - 192,
            top: mousePosition.y - 192,
          }}
        ></div>
      </div>

      {/* Importance of IKIAs */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2
              className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-4"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Importance of IKIAs
            </h2>
            <p
              className="text-gray-600 max-w-2xl mx-auto"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Understanding the critical role of Indigenous Knowledge and Intellectual Assets in
              sustainable development
            </p>
          </div>

          <div className="relative max-w-5xl mx-auto">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-[#7E2518] via-[#E8B32C] to-[#159147] rounded-full hidden lg:block"></div>

            {importancePoints.map((point, index) => {
              const Icon = point.icon
              const isEven = index % 2 === 0
              const colors = ['#7E2518', '#159147', '#E8B32C', '#C86E36', '#81B1DB']
              const bgColor = colors[index % colors.length]

              return (
                <div
                  key={index}
                  className={`relative flex items-center mb-12 ${
                    isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'
                  } flex-col lg:gap-0 gap-6`}
                >
                  {/* Content Card */}
                  <div className={`w-full lg:w-5/12 ${isEven ? 'lg:pr-8' : 'lg:pl-8'}`}>
                    <Card className="bg-white border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4 mb-4">
                          <div
                            className="w-12 h-12 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: bgColor }}
                          >
                            <Icon className="w-6 h-6 text-white" />
                          </div>
                          <h3
                            className="font-bold text-[#7E2518] text-lg"
                            style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                          >
                            {point.title}
                          </h3>
                        </div>
                        <p
                          className="text-gray-600 leading-relaxed"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {point.description}
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Center Circle */}
                  <div
                    className="absolute left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full border-4 border-white shadow-lg hidden lg:block"
                    style={{ backgroundColor: bgColor }}
                  ></div>

                  {/* Spacer */}
                  <div className="w-full lg:w-5/12 hidden lg:block"></div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Strategic Alignment */}
      <section className="py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
            {/* Left - Overlapping Circles */}
            <div className="relative h-96 flex items-center justify-center order-2 lg:order-1">
              <div className="relative">
                {/* Main Circle */}
                <div className="w-56 h-56 bg-[#7E2518]/10 rounded-full border-4 border-[#7E2518] flex items-center justify-center shadow-lg">
                  <span
                    className="text-[#7E2518] font-bold text-center text-lg px-4"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    Kenya Vision 2030
                  </span>
                </div>

                {/* Overlapping Circles */}
                <div className="absolute -top-12 -right-12 w-32 h-32 bg-[#E8B32C]/10 rounded-full border-4 border-[#E8B32C] shadow-lg"></div>
                <div className="absolute -bottom-12 -right-12 w-32 h-32 bg-[#81B1DB]/10 rounded-full border-4 border-[#81B1DB] shadow-lg"></div>
                <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-[#159147]/10 rounded-full border-4 border-[#159147] shadow-lg"></div>
              </div>
            </div>

            {/* Right - Content */}
            <div className="space-y-8 order-1 lg:order-2">
              <div>
                <h2
                  className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-4"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  Strategic Alignment
                </h2>
                <p
                  className="text-lg text-gray-700 leading-relaxed"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  The IKIA Conference Initiative is a flagship program under Kenya Vision 2030,
                  creating synergies across multiple strategic frameworks.
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <CheckCircle className="w-6 h-6 text-[#7E2518] mt-1 flex-shrink-0" />
                  <div>
                    <strong
                      className="text-[#7E2518] text-lg"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      Kenya Vision 2030
                    </strong>
                    <p
                      className="text-gray-700 mt-1"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Economic & social transformation through innovation and sustainable
                      development
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <CheckCircle className="w-6 h-6 text-[#7E2518] mt-1 flex-shrink-0" />
                  <div>
                    <strong
                      className="text-[#7E2518] text-lg"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      BetA Initiative
                    </strong>
                    <p
                      className="text-gray-700 mt-1"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Sustainable economic empowerment and community-driven development
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <CheckCircle className="w-6 h-6 text-[#7E2518] mt-1 flex-shrink-0" />
                  <div>
                    <strong
                      className="text-[#7E2518] text-lg"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      NMK Strategic Plan
                    </strong>
                    <p
                      className="text-gray-700 mt-1"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Heritage preservation & innovation through cultural knowledge systems
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Target Audience */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-6">
              <Users className="w-8 h-8 text-[#7E2518]" />
              <h2
                className="text-3xl md:text-4xl font-bold text-[#7E2518]"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                Target Audience & Expected Outcomes
              </h2>
            </div>
            <p
              className="text-gray-600 max-w-2xl mx-auto"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Here are the Targeted Audience and Expected Outcomes from the IKIA Investment
              Conference
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {targetAudience.map((audience, index) => {
              const Icon = audience.icon
              return (
                <Card
                  key={index}
                  className="bg-white border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden"
                >
                  <div className={`${audience.bgColor} text-white p-6`}>
                    <div className="flex items-center gap-4 mb-3">
                      <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                        <span
                          className="text-sm font-bold"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          {audience.number}
                        </span>
                      </div>
                      <Icon className="w-6 h-6" />
                    </div>
                    <h3
                      className="font-bold text-lg mb-2"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      {audience.title}
                    </h3>
                  </div>
                  <CardContent className="p-6">
                    <p
                      className="text-gray-600"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {audience.subtitle}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>
    </div>
  )
}
