"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Clock, Calendar } from 'lucide-react';

interface CountdownCardProps {
  targetDate: Date;
  className?: string;
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

export default function CountdownCard({ targetDate, className }: CountdownCardProps) {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const difference = +targetDate - +new Date();
      
      if (difference > 0) {
        setTimeLeft({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
          minutes: Math.floor((difference / 1000 / 60) % 60),
          seconds: Math.floor((difference / 1000) % 60),
        });
      }
    };

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [targetDate]);

  return (
    <Card 
      className={`transform -skew-x-3 hover:skew-x-0 transition-transform duration-500 bg-gradient-to-br from-secondary/10 to-primary/10 border-2 border-secondary/20 shadow-xl hover:shadow-2xl ${className}`}
    >
      <CardContent className="transform skew-x-3 hover:skew-x-0 transition-transform duration-500 p-8">
        {/* Header */}
        <div className="flex items-center justify-center space-x-2 mb-6">
          <Clock className="w-6 h-6 text-secondary" />
          <h3 className="font-acquire font-bold text-xl text-primary">
            Conference Countdown
          </h3>
        </div>

        {/* Countdown Display */}
        <div className="grid grid-cols-4 gap-4 mb-8">
          <div className="text-center">
            <div className="bg-primary text-primary-foreground rounded-lg p-4 mb-2">
              <span className="font-acquire font-bold text-2xl">
                {timeLeft.days.toString().padStart(2, '0')}
              </span>
            </div>
            <span className="font-myriad text-sm text-muted-foreground uppercase tracking-wide">
              Days
            </span>
          </div>
          
          <div className="text-center">
            <div className="bg-secondary text-secondary-foreground rounded-lg p-4 mb-2">
              <span className="font-acquire font-bold text-2xl">
                {timeLeft.hours.toString().padStart(2, '0')}
              </span>
            </div>
            <span className="font-myriad text-sm text-muted-foreground uppercase tracking-wide">
              Hours
            </span>
          </div>
          
          <div className="text-center">
            <div className="bg-accent text-accent-foreground rounded-lg p-4 mb-2">
              <span className="font-acquire font-bold text-2xl">
                {timeLeft.minutes.toString().padStart(2, '0')}
              </span>
            </div>
            <span className="font-myriad text-sm text-muted-foreground uppercase tracking-wide">
              Minutes
            </span>
          </div>
          
          <div className="text-center">
            <div className="bg-primary/80 text-primary-foreground rounded-lg p-4 mb-2">
              <span className="font-acquire font-bold text-2xl">
                {timeLeft.seconds.toString().padStart(2, '0')}
              </span>
            </div>
            <span className="font-myriad text-sm text-muted-foreground uppercase tracking-wide">
              Seconds
            </span>
          </div>
        </div>

        {/* Conference Date */}
        <div className="flex items-center justify-center space-x-2 mb-6 text-muted-foreground">
          <Calendar className="w-4 h-4" />
          <span className="font-myriad text-sm">
            {targetDate.toLocaleDateString('en-US', { 
              weekday: 'long',
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </span>
        </div>

        {/* Register Button */}
        <div className="flex justify-center">
          <button className="bg-secondary text-secondary-foreground font-myriad font-bold px-8 py-4 rounded-sm hover:bg-secondary/90 hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 w-full max-w-xs">
            Register Now
          </button>
        </div>
      </CardContent>
    </Card>
  );
}
