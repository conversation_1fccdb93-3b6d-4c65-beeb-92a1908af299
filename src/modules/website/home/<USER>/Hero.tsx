import { conferenceInfo, conferenceDate } from "../data";
import CountdownCard from "./CountdownCard";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowRight, Users, Calendar } from "lucide-react";

export default function Hero() {
  return (
    <section className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-6 py-16">
        {/* Main Hero Container - Left Right Layout */}
        <div className="flex flex-col lg:flex-row items-center justify-between min-h-[80vh] gap-12">
          {/* Left Side - CTA Content */}
          <div className="flex flex-col space-y-8 lg:w-1/2">
            {/* Badge */}
            <div className="flex">
              <span className="bg-secondary text-secondary-foreground font-myriad font-semibold px-4 py-2 rounded-full text-sm">
                🇰🇪 IKIA Conference 2025
              </span>
            </div>

            {/* Main Title */}
            <div className="flex flex-col space-y-3">
              <h1 className="font-acquire font-bold text-2xl lg:text-3xl text-primary leading-tight">
                {conferenceInfo.title}
              </h1>
              <h2 className="font-acquire font-bold text-xl lg:text-2xl text-secondary">
                {conferenceInfo.subtitle}
              </h2>
              <p className="font-acquire text-base lg:text-lg text-muted-foreground">
                {conferenceInfo.description}
              </p>
            </div>

            {/* Call to Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 lg:justify-start">
              <Link href="/registration">
                <Button
                  size="lg"
                  className="w-full sm:w-auto px-8 py-4 text-lg font-semibold bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"
                >
                  <Users className="w-5 h-5 mr-2" />
                  Register Now
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>

              <Link href="/about">
                <Button
                  variant="outline"
                  size="lg"
                  className="w-full sm:w-auto px-8 py-4 text-lg font-semibold border-2 border-secondary text-secondary hover:bg-secondary hover:text-secondary-foreground transition-all duration-300"
                >
                  <Calendar className="w-5 h-5 mr-2" />
                  Learn More
                </Button>
              </Link>
            </div>

            {/* Countdown Card */}
            <div className="flex justify-center lg:justify-start">
              <CountdownCard
                targetDate={conferenceDate}
                className="max-w-md w-full"
              />
            </div>
          </div>

          {/* Right Side - Image Placeholder */}
          <div className="flex lg:w-1/2 justify-center lg:justify-end">
            <div className="w-full max-w-lg aspect-square bg-gradient-to-br from-secondary/10 to-primary/10 rounded-2xl border-2 border-dashed border-muted-foreground/30 flex flex-col items-center justify-center space-y-4">
              <div className="w-24 h-24 bg-secondary/20 rounded-full flex items-center justify-center">
                <span className="text-4xl">🌿</span>
              </div>
              <div className="text-center space-y-2">
                <p className="font-acquire font-bold text-lg text-primary">
                  Conference Visual
                </p>
                <p className="font-myriad text-sm text-muted-foreground">
                  Image placeholder
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
