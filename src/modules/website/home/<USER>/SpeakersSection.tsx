import { Card, CardContent } from '@/components/ui/card';
import { User } from 'lucide-react';

interface Speaker {
  id: number;
  name: string;
  title: string;
  organization: string;
  image?: string;
}

export default function SpeakersSection() {
  const speakers: Speaker[] = [
    {
      id: 1,
      name: "<PERSON>",
      title: "Director of Research",
      organization: "IKIA Foundation"
    },
    {
      id: 2,
      name: "<PERSON>",
      title: "Chief Economist",
      organization: "Kenya Investment Authority"
    },
    {
      id: 3,
      name: "<PERSON><PERSON>",
      title: "Indigenous Knowledge Expert",
      organization: "Traditional Medicine Board"
    },
    {
      id: 4,
      name: "<PERSON>",
      title: "Investment Strategist",
      organization: "Natural Products Ventures"
    },
    {
      id: 5,
      name: "<PERSON>",
      title: "Biodiversity Specialist",
      organization: "Kenya Wildlife Service"
    },
    {
      id: 6,
      name: "<PERSON>",
      title: "Trade Development",
      organization: "Export Promotion Council"
    },
    {
      id: 7,
      name: "<PERSON>",
      title: "Innovation Director",
      organization: "Technology Transfer Office"
    },
    {
      id: 8,
      name: "<PERSON>",
      title: "Policy Advisor",
      organization: "Ministry of Trade"
    }
  ];

  return (
    <section className="py-20 bg-muted/30">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="font-acquire font-bold text-sm uppercase tracking-wider text-muted-foreground mb-4">
            SPEAKERS
          </h2>
          <h3 className="font-acquire font-bold text-3xl lg:text-4xl text-primary mb-6">
            Who&apos;s Speaking?
          </h3>
          <p className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto">
            Distinguished experts, thought leaders, and practitioners sharing insights on 
            indigenous knowledge commercialization and sustainable investment opportunities.
          </p>
        </div>

        {/* Speakers Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {speakers.map((speaker) => (
            <Card 
              key={speaker.id}
              className="border-2 border-muted hover:border-secondary/40 transition-all duration-300 hover:shadow-lg group bg-background"
            >
              <CardContent className="p-6 text-center space-y-4">
                {/* Speaker Photo Placeholder */}
                <div className="flex justify-center">
                  <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center group-hover:bg-secondary/10 transition-colors border-2 border-muted-foreground/20">
                    <User className="w-12 h-12 text-muted-foreground/60" />
                  </div>
                </div>
                
                {/* Speaker Info */}
                <div className="space-y-2">
                  <h4 className="font-acquire font-bold text-lg text-primary">
                    {speaker.name}
                  </h4>
                  <p className="font-myriad text-sm text-secondary font-semibold">
                    {speaker.title}
                  </p>
                  <p className="font-myriad text-xs text-muted-foreground">
                    {speaker.organization}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <button className="bg-secondary text-secondary-foreground font-myriad font-bold px-8 py-4 rounded-sm hover:bg-secondary/90 hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2">
            View All Speakers
          </button>
        </div>
      </div>
    </section>
  );
}
