import { Card, CardContent } from '@/components/ui/card';
import { Clock, MapPin, Users } from 'lucide-react';

interface AgendaItem {
  id: number;
  time: string;
  title: string;
  description: string;
  location: string;
  type: 'keynote' | 'panel' | 'workshop' | 'networking' | 'exhibition';
  speakers?: string[];
}

export default function AgendaSection() {
  const agendaItems: AgendaItem[] = [
    {
      id: 1,
      time: "08:00 - 09:00",
      title: "Registration & Welcome Coffee",
      description: "Conference registration and networking breakfast",
      location: "Main Lobby",
      type: "networking"
    },
    {
      id: 2,
      time: "09:00 - 09:30",
      title: "Opening Ceremony",
      description: "Welcome address and conference overview",
      location: "Main Auditorium",
      type: "keynote",
      speakers: ["Hon. Cabinet Secretary", "IKIA Director"]
    },
    {
      id: 3,
      time: "09:30 - 10:30",
      title: "Keynote: Indigenous Knowledge & Investment",
      description: "The future of traditional knowledge commercialization",
      location: "Main Auditorium",
      type: "keynote",
      speakers: ["<PERSON><PERSON> <PERSON>"]
    },
    {
      id: 4,
      time: "10:30 - 11:00",
      title: "Coffee Break & Exhibition",
      description: "Networking and exhibition viewing",
      location: "Exhibition Hall",
      type: "networking"
    },
    {
      id: 5,
      time: "11:00 - 12:30",
      title: "Panel: Investment Opportunities",
      description: "Exploring viable investment prospects in natural products",
      location: "Conference Room A",
      type: "panel",
      speakers: ["Anna Harrison", "John Powell", "Mary April"]
    },
    {
      id: 6,
      time: "12:30 - 13:30",
      title: "Lunch & Networking",
      description: "Networking lunch with traditional cuisine",
      location: "Dining Hall",
      type: "networking"
    }
  ];

  const getTypeColor = (type: AgendaItem['type']) => {
    switch (type) {
      case 'keynote':
        return 'bg-primary/10 border-primary/30 text-primary';
      case 'panel':
        return 'bg-secondary/10 border-secondary/30 text-secondary';
      case 'workshop':
        return 'bg-accent/10 border-accent/30 text-accent';
      case 'networking':
        return 'bg-muted/30 border-muted text-muted-foreground';
      case 'exhibition':
        return 'bg-yellow-50 border-yellow-200 text-yellow-700';
      default:
        return 'bg-muted/30 border-muted text-muted-foreground';
    }
  };

  return (
    <section className="py-20 bg-muted/30">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="font-acquire font-bold text-sm uppercase tracking-wider text-muted-foreground mb-4">
            📅 Conference Exhibition Day Agenda
          </h2>
          <h3 className="font-acquire font-bold text-3xl lg:text-4xl text-primary mb-6">
            Conference Exhibition Day Agenda
          </h3>
          <p className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto">
            Join us for a full day of insights, networking, and discovery. 
            Explore the complete schedule of keynotes, panels, workshops, and exhibitions.
          </p>
        </div>

        {/* Agenda Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {agendaItems.map((item) => (
            <Card 
              key={item.id}
              className="border-2 border-muted hover:border-secondary/40 transition-all duration-300 hover:shadow-lg group bg-background"
            >
              <CardContent className="p-6 space-y-4">
                {/* Time and Type Badge */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-primary">
                    <Clock className="w-4 h-4" />
                    <span className="font-myriad font-bold text-sm">
                      {item.time}
                    </span>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-myriad font-semibold border ${getTypeColor(item.type)}`}>
                    {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                  </span>
                </div>

                {/* Title */}
                <h4 className="font-acquire font-bold text-lg text-primary leading-tight">
                  {item.title}
                </h4>

                {/* Description */}
                <p className="font-myriad text-sm text-muted-foreground leading-relaxed">
                  {item.description}
                </p>

                {/* Location */}
                <div className="flex items-center space-x-2 text-secondary">
                  <MapPin className="w-4 h-4" />
                  <span className="font-myriad text-sm font-medium">
                    {item.location}
                  </span>
                </div>

                {/* Speakers */}
                {item.speakers && item.speakers.length > 0 && (
                  <div className="flex items-start space-x-2 text-muted-foreground">
                    <Users className="w-4 h-4 mt-0.5" />
                    <div className="flex flex-wrap gap-1">
                      {item.speakers.map((speaker, index) => (
                        <span 
                          key={index}
                          className="font-myriad text-xs bg-muted/50 px-2 py-1 rounded"
                        >
                          {speaker}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View Full Agenda Button */}
        <div className="text-center">
          <button className="bg-secondary text-secondary-foreground font-myriad font-bold px-8 py-4 rounded-sm hover:bg-secondary/90 hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2">
            View Full Day Agenda
          </button>
        </div>
      </div>
    </section>
  );
}
