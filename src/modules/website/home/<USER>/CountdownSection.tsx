'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

export default function CountdownSection() {
  // Conference date: September 15, 2025
  const conferenceDate = new Date('2025-09-15T09:00:00').getTime();
  
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  const calculateTimeLeft = (): TimeLeft => {
    const now = new Date().getTime();
    const difference = conferenceDate - now;

    if (difference > 0) {
      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((difference % (1000 * 60)) / 1000)
      };
    }

    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  };

  useEffect(() => {
    // Set initial time
    setTimeLeft(calculateTimeLeft());

    // Update every second
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const timeUnits = [
    { label: 'Days', value: timeLeft.days },
    { label: 'Hours', value: timeLeft.hours },
    { label: 'Minutes', value: timeLeft.minutes },
    { label: 'Seconds', value: timeLeft.seconds }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="font-acquire font-bold text-3xl lg:text-4xl text-primary mb-6 leading-tight">
            Countdown to Kenya&apos;s First IKIA
            <br />
            Investment Conference
          </h2>
          <p className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto">
            Join us for this historic event that will shape the future of indigenous knowledge 
            commercialization and sustainable investment in Kenya.
          </p>
        </div>

        {/* Countdown Display */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8 max-w-4xl mx-auto mb-12">
          {timeUnits.map((unit, index) => (
            <Card 
              key={unit.label}
              className="border-2 border-secondary/20 bg-secondary/5 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg"
            >
              <CardContent className="p-6 lg:p-8 text-center space-y-2">
                <div className="font-acquire font-bold text-4xl lg:text-5xl xl:text-6xl text-primary">
                  {unit.value.toString().padStart(2, '0')}
                </div>
                <div className="font-myriad text-sm lg:text-base text-muted-foreground uppercase tracking-wider">
                  {unit.label}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Conference Date Display */}
        <div className="text-center space-y-4">
          <div className="inline-flex items-center space-x-3 bg-muted/50 rounded-lg px-6 py-4 border border-muted">
            <div className="w-3 h-3 bg-secondary rounded-full animate-pulse"></div>
            <span className="font-myriad text-lg font-semibold text-primary">
              Monday, September 15, 2025
            </span>
          </div>
          <p className="font-myriad text-sm text-muted-foreground">
            Mark your calendar and be part of history
          </p>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-12">
          <div className="space-y-4">
            <button className="bg-primary text-primary-foreground font-myriad font-bold px-8 py-4 rounded-sm hover:bg-primary/90 hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 mr-4">
              Register Now
            </button>
            <button className="bg-transparent border-2 border-secondary text-secondary font-myriad font-bold px-8 py-4 rounded-sm hover:bg-secondary hover:text-secondary-foreground transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2">
              Learn More
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
