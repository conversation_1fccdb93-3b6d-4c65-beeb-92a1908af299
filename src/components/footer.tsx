'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import {
  Phone,
  Mail,
  MapPin,
  Calendar,
  Users,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  Youtube,
  ArrowRight,
  Globe,
  Clock,
  Send,
} from 'lucide-react'
import Image from 'next/image'

export default function Footer() {
  const quickLinks = [
    { name: 'About Conference', href: '/about' },
    { name: 'Program & Agenda', href: '/program' },
    { name: 'Speakers', href: '/speakers' },
    { name: 'Registration', href: '/register' },
    { name: 'Exhibitions', href: '/exhibitions' },
    { name: 'Accommodation', href: '/accommodation' },
  ]

  const supportLinks = [
    { name: 'Contact Us', href: '/contact' },
    { name: 'FAQ', href: '/faq' },
    { name: 'Terms & Conditions', href: '/terms' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Code of Conduct', href: '/conduct' },
    { name: 'Accessibility', href: '/accessibility' },
  ]

  const socialLinks = [
    { icon: Facebook, href: '#', color: '#1877F2' },
    { icon: Twitter, href: '#', color: '#1DA1F2' },
    { icon: Linkedin, href: '#', color: '#0A66C2' },
    { icon: Instagram, href: '#', color: '#E4405F' },
    { icon: Youtube, href: '#', color: '#FF0000' },
  ]

  const sponsors = [
    { name: 'Kenya Vision 2030', logo: '/placeholder.svg?height=60&width=120&text=Vision+2030' },
    { name: 'BetA', logo: '/placeholder.svg?height=60&width=120&text=BetA' },
    { name: 'NMK', logo: '/placeholder.svg?height=60&width=120&text=NMK' },
    { name: 'Partner 4', logo: '/placeholder.svg?height=60&width=120&text=Partner' },
  ]

  return (
    <footer className="relative bg-gradient-to-br from-[#7E2518] via-[#7E2518] to-[#159147] text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#E8B32C] rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#81B1DB] rounded-full blur-3xl transform translate-x-1/2 translate-y-1/2"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-[#C86E36] rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute top-1/4 right-1/4 w-48 h-48 bg-[#159147] rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10">
        {/* Top accent line */}
        <div className="h-1 bg-gradient-to-r from-[#E8B32C] via-[#C86E36] to-[#81B1DB]"></div>

        {/* Main Footer Content */}
        <div className="container mx-auto px-4 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Conference Info & Logo */}
            <div className="lg:col-span-1 space-y-6">
              <div className="bg-white/15 backdrop-blur-sm rounded-lg p-4 w-fit border border-white/10">
                <Image
                  src="/logo.png"
                  alt="IKIA Conference"
                  width={200}
                  height={60}
                  className="h-12 w-auto filter brightness-110"
                />
              </div>

              <div className="space-y-4">
                <h3
                  className="text-xl font-bold text-[#E8B32C]"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  IKIA Investment Conference 2024
                </h3>
                <p
                  className="text-white/85 leading-relaxed"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Bridging Indigenous Knowledge with Modern Innovation for Sustainable Development
                </p>
              </div>

              {/* Conference Details */}
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-white/90">
                  <Calendar className="w-5 h-5 text-[#E8B32C]" />
                  <span className="text-sm">March 15-17, 2024</span>
                </div>
                <div className="flex items-center gap-3 text-white/90">
                  <MapPin className="w-5 h-5 text-[#E8B32C]" />
                  <span className="text-sm">Nairobi, Kenya</span>
                </div>
                <div className="flex items-center gap-3 text-white/90">
                  <Users className="w-5 h-5 text-[#E8B32C]" />
                  <span className="text-sm">500+ Expected Attendees</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="space-y-6">
              <h3
                className="text-lg font-semibold text-[#E8B32C]"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                Quick Links
              </h3>
              <ul className="space-y-3">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      className="flex items-center gap-2 text-white/85 hover:text-white hover:translate-x-1 transition-all duration-300 group"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      <ArrowRight className="w-4 h-4 text-[#E8B32C] group-hover:text-[#C86E36] transition-colors" />
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support & Legal */}
            <div className="space-y-6">
              <h3
                className="text-lg font-semibold text-[#E8B32C]"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                Support & Legal
              </h3>
              <ul className="space-y-3">
                {supportLinks.map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      className="flex items-center gap-2 text-white/85 hover:text-white hover:translate-x-1 transition-all duration-300 group"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      <ArrowRight className="w-4 h-4 text-[#E8B32C] group-hover:text-[#C86E36] transition-colors" />
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact & Newsletter */}
            <div className="space-y-6">
              <h3
                className="text-lg font-semibold text-[#E8B32C]"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                Stay Connected
              </h3>

              {/* Contact Info */}
              <div className="space-y-4">
                <div className="flex items-center gap-3 text-white/90">
                  <Phone className="w-5 h-5 text-[#E8B32C]" />
                  <span className="text-sm">+254 70 333 555</span>
                </div>
                <div className="flex items-center gap-3 text-white/90">
                  <Mail className="w-5 h-5 text-[#E8B32C]" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-white/90">
                  <Globe className="w-5 h-5 text-[#E8B32C]" />
                  <span className="text-sm">www.ikiaconference.ke</span>
                </div>
              </div>

              {/* Newsletter Signup */}
              <Card className="bg-white/15 backdrop-blur-sm border-white/20">
                <CardContent className="p-4">
                  <h4
                    className="font-semibold mb-3 text-[#E8B32C]"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    Newsletter
                  </h4>
                  <p className="text-sm text-white/85 mb-4">Get latest updates and announcements</p>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter your email"
                      className="bg-white/20 border-white/30 text-white placeholder:text-white/60 focus:border-[#E8B32C]"
                    />
                    <Button
                      size="sm"
                      className="bg-gradient-to-r from-[#E8B32C] to-[#C86E36] hover:from-[#C86E36] hover:to-[#E8B32C] border-0 px-3 shadow-lg hover:shadow-[#E8B32C]/25"
                    >
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Social Media & Sponsors Section */}
          <div className="mt-16 pt-8 border-t border-white/20">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              {/* Social Media */}
              <div className="space-y-4">
                <h4
                  className="font-semibold text-[#E8B32C]"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  Follow Us
                </h4>
                <div className="flex gap-4">
                  {socialLinks.map((social, index) => {
                    const Icon = social.icon
                    return (
                      <a
                        key={index}
                        href={social.href}
                        className="w-10 h-10 bg-white/15 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/25 transition-all duration-300 hover:scale-110 border border-white/10"
                        style={{ color: social.color }}
                      >
                        <Icon className="w-5 h-5" />
                      </a>
                    )
                  })}
                </div>
              </div>

              {/* Sponsors */}
              <div className="space-y-4">
                <h4
                  className="font-semibold text-[#E8B32C]"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  Our Partners
                </h4>
                <div className="flex flex-wrap gap-4 items-center">
                  {sponsors.map((sponsor, index) => (
                    <div
                      key={index}
                      className="bg-white/15 backdrop-blur-sm rounded-lg p-3 hover:bg-white/25 transition-all duration-300 border border-white/10"
                    >
                      <Image
                        src={sponsor.logo || '/placeholder.svg'}
                        alt={sponsor.name}
                        width={120}
                        height={60}
                        className="h-8 w-auto opacity-85 hover:opacity-100 transition-opacity"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="mt-12 pt-8 border-t border-white/20">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div
                className="text-sm text-white/75"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                © 2024 IKIA Conference. All rights reserved. | Designed with ❤️ for Indigenous
                Knowledge
              </div>
              <div className="flex items-center gap-4 text-sm text-white/75">
                <Clock className="w-4 h-4" />
                <span>Last updated: January 2024</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
